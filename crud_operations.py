import sqlite3
import tkinter as tk
from tkinter import messagebox, ttk

# Function to connect to the database
def connect_db():
    return sqlite3.connect('simple_system.db')

# Function to execute SQL commands
def execute_query(query, params=()):
    conn = connect_db()
    c = conn.cursor()
    c.execute(query, params)
    conn.commit()
    conn.close()

# Function to fetch all records
def fetch_all_records(query):
    conn = connect_db()
    c = conn.cursor()
    c.execute(query)
    records = c.fetchall()
    conn.close()
    return records

# Function to add an employee
def add_employee():
    name, age, dept = entry_name.get().strip(), entry_age.get().strip(), entry_dept.get().strip()
    if not name or not age or not dept:
        messagebox.showwarning("Warning", "Please fill all fields.")
        return

    try:
        age_int = int(age)
        if age_int <= 0:
            raise ValueError("Age must be a positive number.")
    except ValueError:
        messagebox.showwarning("Warning", "Age must be a valid number.")
        return

    execute_query("INSERT INTO Employees (name, age, department) VALUES (?, ?, ?)",
                  (name, age_int, dept))
    messagebox.showinfo("Success", "Employee added successfully!")
    show_all_employees()
    clear_entries()

# Function to show all employees
def show_all_employees():
    tree.delete(*tree.get_children())  # Clear existing data
    for record in fetch_all_records("SELECT * FROM Employees"):
        tree.insert("", "end", values=record)

# Function to delete an employee
def delete_employee():
    selected = tree.selection()
    if selected:
        emp_id = tree.item(selected[0])['values'][0]  # Get ID of selected employee
        confirmed = messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete employee ID {emp_id}?")
        if confirmed:
            execute_query("DELETE FROM Employees WHERE id=?", (emp_id,))
            messagebox.showinfo("Success", "Employee deleted successfully!")
            show_all_employees()

# Function to update an employee
def update_employee():
    selected = tree.selection()
    if selected:
        emp_id = tree.item(selected[0])['values'][0]  # Get ID of selected employee
        new_name, new_age, new_dept = entry_name.get().strip(), entry_age.get().strip(), entry_dept.get().strip()

        if new_name and new_age and new_dept:
            try:
                new_age_int = int(new_age)
                if new_age_int <= 0:
                    raise ValueError("Age must be a positive number.")
            except ValueError:
                messagebox.showwarning("Warning", "Age must be a valid number.")
                return

            execute_query("UPDATE Employees SET name=?, age=?, department=? WHERE id=?",
                          (new_name, new_age_int, new_dept, emp_id))
            messagebox.showinfo("Success", "Employee updated successfully!")
            show_all_employees()
            clear_entries()
        else:
            messagebox.showwarning("Warning", "Please fill all fields.")
    else:
        messagebox.showwarning("Warning", "Select an employee to update.")

# Function to generate a report
def generate_report():
    records = fetch_all_records("SELECT * FROM Employees")
    if not records:
        messagebox.showinfo("Report", "No employees found in the database.")
        return

    report = "EMPLOYEE REPORT\n" + "=" * 50 + "\n\n"
    report += f"Total Employees: {len(records)}\n\n"

    departments = {}
    for record in records:
        dept = record[3]
        if dept not in departments:
            departments[dept] = []
        departments[dept].append(record)

    report += "Employees by Department:\n" + "-" * 30 + "\n"
    for dept, employees in departments.items():
        report += f"\n{dept.upper()} ({len(employees)} employees):\n"
        for emp in employees:
            report += f"  - {emp[1]} (Age: {emp[2]})\n"

    report_window = tk.Toplevel(root)
    report_window.title("Employee Report")
    report_window.geometry("500x600")
    report_window.configure(bg='#f0f0f0')

    text_widget = tk.Text(report_window, wrap=tk.WORD, padx=10, pady=10, font=('Courier', 10), bg='white')
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

    scrollbar = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=text_widget.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    text_widget.configure(yscrollcommand=scrollbar.set)

    text_widget.insert(tk.END, report)
    text_widget.config(state=tk.DISABLED)

    tk.Button(report_window, text="Close", command=report_window.destroy,
              bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'), padx=20, pady=5).pack()

# Function to clear entry fields
def clear_entries():
    entry_name.delete(0, tk.END)
    entry_age.delete(0, tk.END)
    entry_dept.delete(0, tk.END)

# Initialize GUI
root = tk.Tk()
root.title("Employee Management System")
root.geometry("900x700")
root.configure(bg='#f0f0f0')

main_frame = tk.Frame(root, bg='#f0f0f0')
main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

title_label = tk.Label(main_frame, text="Employee Management System", font=('Arial', 16, 'bold'),
                       bg='#f0f0f0', fg='#2c3e50')
title_label.pack(pady=(0, 20))

left_frame = tk.Frame(main_frame, bg='#f0f0f0')
left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))

input_frame = tk.LabelFrame(left_frame, text="Employee Information", font=('Arial', 12, 'bold'),
                            bg='#f0f0f0', fg='#2c3e50')
input_frame.pack(fill=tk.X, pady=(0, 20))

tk.Label(input_frame, text="Name:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10, pady=(10, 0))
entry_name = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_name.pack(padx=10, pady=(0, 10))

tk.Label(input_frame, text="Age:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10)
entry_age = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_age.pack(padx=10, pady=(0, 10))

tk.Label(input_frame, text="Department:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10)
entry_dept = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_dept.pack(padx=10, pady=(0, 10))

button_frame = tk.LabelFrame(left_frame, text="Actions", font=('Arial', 12, 'bold'),
                             bg='#f0f0f0', fg='#2c3e50')
button_frame.pack(fill=tk.X)

btn_style = {'font': ('Arial', 10, 'bold'), 'width': 20, 'pady': 5}

tk.Button(button_frame, text="Add Employee", command=add_employee, bg='#27ae60', fg='white', **btn_style).pack(
    padx=10, pady=5)
tk.Button(button_frame, text="Update Employee", command=update_employee, bg='#3498db', fg='white', **btn_style).pack(
    padx=10, pady=5)
tk.Button(button_frame, text="Delete Employee", command=delete_employee, bg='#e74c3c', fg='white', **btn_style).pack(
    padx=10, pady=5)
tk.Button(button_frame, text="Clear Fields", command=clear_entries, bg='#95a5a6', fg='white', **btn_style).pack(
    padx=10, pady=5)
tk.Button(button_frame, text="Generate Report", command=generate_report, bg='#9b59b6', fg='white', **btn_style).pack(
    padx=10, pady=5)

right_frame = tk.Frame(main_frame, bg='#f0f0f0')
right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

table_frame = tk.LabelFrame(right_frame, text="Employee Records", font=('Arial', 12, 'bold'),
                            bg='#f0f0f0', fg='#2c3e50')
table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 20))

tree_columns = ("ID", "Name", "Age", "Department")
tree = ttk.Treeview(table_frame, columns=tree_columns, show="headings", selectmode="browse")
for col in tree_columns:
    tree.heading(col, text=col)
    tree.column(col, minwidth=0, width=150, stretch=tk.NO)
tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# Populate the Treeview initially
show_all_employees()

scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
tree.configure(yscrollcommand=scrollbar.set)

root.mainloop()
