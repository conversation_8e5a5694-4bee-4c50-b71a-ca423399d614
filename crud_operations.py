import sqlite3
import tkinter as tk
from tkinter import messagebox

# Function to connect to the database
def connect_db():
    return sqlite3.connect('simple_system.db')

# Function to create a new record
def add_employee():
    conn = connect_db()
    c = conn.cursor()
    c.execute("INSERT INTO Employees (name, age, department) VALUES (?, ?, ?)",
              (entry_name.get(), entry_age.get(), entry_dept.get()))
    conn.commit()
    conn.close()
    messagebox.showinfo("Success", "Employee added successfully!")
    show_all_employees()

# Function to show all employees
def show_all_employees():
    conn = connect_db()
    c = conn.cursor()
    c.execute("SELECT * FROM Employees")
    records = c.fetchall()
    conn.close()
    listbox.delete(0, tk.END)
    for record in records:
        listbox.insert(tk.END, f"ID: {record[0]}, Name: {record[1]}, Age: {record[2]}, Department: {record[3]}")

# Function to delete a record
def delete_employee():
    selected = listbox.curselection()
    if selected:
        emp_id = listbox.get(selected[0]).split(",")[0].split(":")[1].strip()
        conn = connect_db()
        c = conn.cursor()
        c.execute("DELETE FROM Employees WHERE id=?", (emp_id,))
        conn.commit()
        conn.close()
        messagebox.showinfo("Success", "Employee deleted successfully!")
        show_all_employees()
    else:
        messagebox.showwarning("Warning", "Select an employee to delete.")

# Set up the Tkinter GUI
root = tk.Tk()
root.title("Simple CRUD System")

# Entry fields
tk.Label(root, text="Name").pack()
entry_name = tk.Entry(root)
entry_name.pack()

tk.Label(root, text="Age").pack()
entry_age = tk.Entry(root)
entry_age.pack()

tk.Label(root, text="Department").pack()
entry_dept = tk.Entry(root)
entry_dept.pack()

# Buttons
tk.Button(root, text="Add Employee", command=add_employee).pack()
tk.Button(root, text="Show All Employees", command=show_all_employees).pack()
tk.Button(root, text="Delete Employee", command=delete_employee).pack()
tk.Button(root, text="Generate Report", command=generate_report).pack()

# Listbox to display employee records
listbox = tk.Listbox(root, width=50, height=15)
listbox.pack()

# Run the application
root.mainloop()


