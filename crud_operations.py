import sqlite3
import tkinter as tk
from tkinter import messagebox, ttk

# Function to connect to the database
def connect_db():
    return sqlite3.connect('simple_system.db')

# Function to create a new record
def add_employee():
    name = entry_name.get().strip()
    age = entry_age.get().strip()
    dept = entry_dept.get().strip()

    if not name or not age or not dept:
        messagebox.showwarning("Warning", "Please fill all fields.")
        return

    try:
        age_int = int(age)
        if age_int <= 0:
            messagebox.showwarning("Warning", "Age must be a positive number.")
            return
    except ValueError:
        messagebox.showwarning("Warning", "Age must be a valid number.")
        return

    conn = connect_db()
    c = conn.cursor()
    c.execute("INSERT INTO Employees (name, age, department) VALUES (?, ?, ?)",
              (name, age_int, dept))
    conn.commit()
    conn.close()
    messagebox.showinfo("Success", "Employee added successfully!")
    show_all_employees()
    clear_entries()

# Function to show all employees
def show_all_employees():
    conn = connect_db()
    c = conn.cursor()
    c.execute("SELECT * FROM Employees")
    records = c.fetchall()
    conn.close()

    # Clear existing data
    for item in tree.get_children():
        tree.delete(item)

    # Insert new data
    for record in records:
        tree.insert("", "end", values=record)

# Function to delete a record
def delete_employee():
    selected = tree.selection()
    if selected:
        item = tree.item(selected[0])
        emp_id = item['values'][0]  # Get the ID from the first column

        result = messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete employee ID {emp_id}?")
        if result:
            conn = connect_db()
            c = conn.cursor()
            c.execute("DELETE FROM Employees WHERE id=?", (emp_id,))
            conn.commit()
            conn.close()
            messagebox.showinfo("Success", "Employee deleted successfully!")
            show_all_employees()
    else:
        messagebox.showwarning("Warning", "Select an employee to delete.")

# Function to update employee
def update_employee():
    selected = tree.selection()
    if selected:
        item = tree.item(selected[0])
        emp_id = item['values'][0]

        # Get new values from entry fields
        new_name = entry_name.get().strip()
        new_age = entry_age.get().strip()
        new_dept = entry_dept.get().strip()

        if new_name and new_age and new_dept:
            try:
                new_age_int = int(new_age)
                if new_age_int <= 0:
                    messagebox.showwarning("Warning", "Age must be a positive number.")
                    return
            except ValueError:
                messagebox.showwarning("Warning", "Age must be a valid number.")
                return

            conn = connect_db()
            c = conn.cursor()
            c.execute("UPDATE Employees SET name=?, age=?, department=? WHERE id=?",
                     (new_name, new_age_int, new_dept, emp_id))
            conn.commit()
            conn.close()
            messagebox.showinfo("Success", "Employee updated successfully!")
            show_all_employees()
            clear_entries()
        else:
            messagebox.showwarning("Warning", "Please fill all fields.")
    else:
        messagebox.showwarning("Warning", "Select an employee to update.")

# Function to generate a report
def generate_report():
    conn = connect_db()
    c = conn.cursor()
    c.execute("SELECT * FROM Employees")
    records = c.fetchall()
    conn.close()

    if not records:
        messagebox.showinfo("Report", "No employees found in the database.")
        return

    # Create a report string
    report = "EMPLOYEE REPORT\n"
    report += "=" * 50 + "\n\n"
    report += f"Total Employees: {len(records)}\n\n"

    # Group by department
    departments = {}
    for record in records:
        dept = record[3]
        if dept not in departments:
            departments[dept] = []
        departments[dept].append(record)

    report += "Employees by Department:\n"
    report += "-" * 30 + "\n"

    for dept, employees in departments.items():
        report += f"\n{dept.upper()} ({len(employees)} employees):\n"
        for emp in employees:
            report += f"  - {emp[1]} (Age: {emp[2]})\n"

    # Show report in a new window
    report_window = tk.Toplevel(root)
    report_window.title("Employee Report")
    report_window.geometry("500x600")
    report_window.configure(bg='#f0f0f0')

    # Create a text widget with scrollbar
    text_widget = tk.Text(report_window, wrap=tk.WORD, padx=10, pady=10,
                          font=('Courier', 10), bg='white')
    scrollbar = ttk.Scrollbar(report_window, orient=tk.VERTICAL, command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)

    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    text_widget.insert(tk.END, report)
    text_widget.config(state=tk.DISABLED)  # Make it read-only

    # Add a close button
    tk.Button(report_window, text="Close", command=report_window.destroy,
              bg='#e74c3c', fg='white', font=('Arial', 10, 'bold'),
              padx=20, pady=5).pack()

# Function to clear entry fields
def clear_entries():
    entry_name.delete(0, tk.END)
    entry_age.delete(0, tk.END)
    entry_dept.delete(0, tk.END)

# Function to load selected employee data into entry fields
def load_employee_data():
    selected = tree.selection()
    if selected:
        item = tree.item(selected[0])
        values = item['values']

        clear_entries()
        entry_name.insert(0, values[1])  # Name
        entry_age.insert(0, values[2])   # Age
        entry_dept.insert(0, values[3])  # Department

# Function to handle table row selection (auto-load data)
def on_tree_select(event):
    load_employee_data()

# Set up the Tkinter GUI
root = tk.Tk()
root.title("Employee Management System")
root.geometry("900x700")
root.configure(bg='#f0f0f0')

# Create main frame
main_frame = tk.Frame(root, bg='#f0f0f0')
main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# Title
title_label = tk.Label(main_frame, text="Employee Management System",
                      font=('Arial', 16, 'bold'), bg='#f0f0f0', fg='#2c3e50')
title_label.pack(pady=(0, 20))

# Create left frame for input fields and buttons
left_frame = tk.Frame(main_frame, bg='#f0f0f0')
left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))

# Input section
input_frame = tk.LabelFrame(left_frame, text="Employee Information",
                           font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
input_frame.pack(fill=tk.X, pady=(0, 20))

# Entry fields with improved styling
tk.Label(input_frame, text="Name:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10, pady=(10, 0))
entry_name = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_name.pack(padx=10, pady=(0, 10))

tk.Label(input_frame, text="Age:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10)
entry_age = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_age.pack(padx=10, pady=(0, 10))

tk.Label(input_frame, text="Department:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w', padx=10)
entry_dept = tk.Entry(input_frame, font=('Arial', 10), width=25)
entry_dept.pack(padx=10, pady=(0, 10))

# Buttons section
button_frame = tk.LabelFrame(left_frame, text="Actions",
                            font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
button_frame.pack(fill=tk.X)

# Styled buttons
btn_style = {'font': ('Arial', 10, 'bold'), 'width': 20, 'pady': 5}

tk.Button(button_frame, text="Add Employee", command=add_employee,
         bg='#27ae60', fg='white', **btn_style).pack(padx=10, pady=5)

tk.Button(button_frame, text="Update Employee", command=update_employee,
         bg='#3498db', fg='white', **btn_style).pack(padx=10, pady=5)

tk.Button(button_frame, text="Delete Employee", command=delete_employee,
         bg='#e74c3c', fg='white', **btn_style).pack(padx=10, pady=5)

?*tk.Button(button_frame, text="Load Selected", command=load_employee_data,
         bg='#f39c12', fg='white', **btn_style).pack(padx=10, pady=5)

tk.Button(button_frame, text="Clear Fields", command=clear_entries,
         bg='#95a5a6', fg='white', **btn_style).pack(padx=10, pady=5)

tk.Button(button_frame, text="Generate Report", command=generate_report,
         bg='#9b59b6', fg='white', **btn_style).pack(padx=10, pady=5)

# Create right frame for the table
right_frame = tk.Frame(main_frame, bg='#f0f0f0')
right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

# Table section using Treeview widget
table_frame = tk.LabelFrame(right_frame, text="Employee Records",
                           font=('Arial', 12, 'bold'), bg='#f0f0f0', fg='#2c3e50')
table_frame.pack(fill=tk.BOTH, expand=True)

# Treeview (table) with scrollbars
tree = ttk.Treeview(table_frame, columns=('ID', 'Name', 'Age', 'Department'),
                    show='headings', height=20)
tree.pack(fill=tk.BOTH, expand=True)

# Configure columns
tree.heading('ID', text='ID')
tree.heading('Name', text='Name')
tree.heading('Age', text='Age')
tree.heading('Department', text='Department')

# Configure column widths and alignment
tree.column('ID', width=50, anchor='center')
tree.column('Name', width=150, anchor='w')
tree.column('Age', width=80, anchor='center')
tree.column('Department', width=150, anchor='w')

# Bind the selection event to automatically load data
tree.bind('<<TreeviewSelect>>', on_tree_select)

# Scrollbars
v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=tree.yview)
v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
tree.configure(yscrollcommand=v_scrollbar.set)

# Load initial data
show_all_employees()

# Run the application
root.mainloop()
