import pandas as pd
import matplotlib.pyplot as plt

def generate_report():
    conn = connect_db()
    df = pd.read_sql_query("SELECT * FROM Employees", conn)
    conn.close()
    
    # Display the DataFrame
    print(df)

    # Generate a simple bar plot for the number of employees in each department
    dept_counts = df['department'].value_counts()
    dept_counts.plot(kind='bar', title='Employee Distribution by Department')
    plt.show()
