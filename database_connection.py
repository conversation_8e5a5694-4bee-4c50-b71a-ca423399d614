import sqlite3

# Connect to the SQLite database (or create it)
conn = sqlite3.connect('simple_system.db')
c = conn.cursor()

# Create a table (if not exists)
c.execute('''CREATE TABLE IF NOT EXISTS Employees (
                id INTEGER PRIMARY KEY,
                name TEXT,
                age INTEGER,
                department TEXT)''')

# Commit and close
conn.commit()
conn.close()
print("Database and table created successfully.")
